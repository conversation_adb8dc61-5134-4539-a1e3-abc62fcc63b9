import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { loadStripe } from "@stripe/stripe-js";
import { SupabaseClient } from "@supabase/supabase-js";
import { Checkbox } from "@/components/ui/checkbox";
import { setSubscriptionCookie, getSubscriptionCookie } from "@/utils/cookieUtils";
import { getSelectedPlanType, getPriceIdForPlanType, clearSelectedPlanType, PLAN_TYPES } from "@/utils/planUtils";

interface OnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  supabase: SupabaseClient;
}

export default function Onboarding({ isOpen, onClose, supabase }: OnboardingProps) {
  // Simplified state for Google auth and pricing only
  const [currentStep, setCurrentStep] = useState(1); // 1 = Google auth, 2 = pricing
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-redirect to Stripe checkout when pricing card is shown, but only if user doesn't have a plan
  useEffect(() => {
    // Check if we're showing the pricing card (isLoggedIn)
    if (isLoggedIn && currentStep === 2) {
      // Set loading state to show user something is happening
      setIsLoading(true);

      let redirectTimerCompleted = false;

      // Add a short delay to check for subscription before redirecting
      const redirectTimer = setTimeout(async () => {
        redirectTimerCompleted = true;

        try {
          // Check if user has an active subscription before redirecting
          const hasActiveSubscription = await checkUserSubscription();

          if (hasActiveSubscription) {
            // Set cookie for future reference
            setSubscriptionCookie(true);
            // Close onboarding
            onClose();
          } else {
            // Set cookie for future reference
            setSubscriptionCookie(false);
            // Redirect to Stripe
            handleSelectPlan('weekly');
          }
        } catch (error) {
          console.error('Error checking subscription status:', error);
          // If there's an error, proceed with redirect to be safe
          handleSelectPlan('weekly');
        }
      }, 1000); // 1 second delay to check subscription status (reduced from 2 seconds)

      return () => {
        if (!redirectTimerCompleted) {
          clearTimeout(redirectTimer);
        }
      };
    }
  }, [isLoggedIn, currentStep, onClose]);

  // Check for returning OAuth users
  useEffect(() => {
    if (!isOpen) return;

    const checkAuthSession = async () => {
      const { data } = await supabase.auth.getSession();

      if (data.session) {
        // User is already logged in, go to pricing card
        setIsLoggedIn(true);
        setCurrentStep(2); // Show pricing card
      }
    };

    checkAuthSession();
  }, [isOpen, supabase.auth]);

  // Handle Google sign in
  const handleGoogleSignIn = async () => {
    try {
      setIsSubmitting(true);

      // Check if there's a selected plan type to preserve through auth flow
      const selectedPlanType = getSelectedPlanType();
      let redirectUrl = window.location.origin + '?subscription_pending=true';

      // Check for no-trial parameters
      const urlParams = new URLSearchParams(window.location.search);
      const basicNoTrial = urlParams.get('basicnotrial') !== null;
      const proNoTrial = urlParams.get('pronotrial') !== null;

      // Add the plan type to the redirect URL if it exists
      if (selectedPlanType === PLAN_TYPES.BASIC || basicNoTrial) {
        // If it's a no-trial basic plan, preserve that parameter
        if (basicNoTrial) {
          redirectUrl += '&basicnotrial';
        } else {
          redirectUrl += '&basic';
        }
      } else if (selectedPlanType === PLAN_TYPES.PRO || proNoTrial) {
        // If it's a no-trial pro plan, preserve that parameter
        if (proNoTrial) {
          redirectUrl += '&pronotrial';
        } else {
          redirectUrl += '&pro';
        }
      }

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            // Add a query parameter to indicate that the user should be redirected to paywall
            skip_onboarding: 'true'
          }
        }
      });

      if (error) throw error;
    } catch (error: any) {
      setLoginError(error.message);
      setIsSubmitting(false);
    }
  };

  // Function to check if user has an active subscription
  const checkUserSubscription = async (): Promise<boolean> => {
    try {
      // Get the session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        return false;
      }

      try {
        // First check the subscriptions table
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', session.user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (subscriptionError) {
          console.error('Error checking subscription:', subscriptionError);
          // Continue to check subscription_mappings even if there's an error here
        } else {
          // Check if user has an active subscription in the subscriptions table
          if (subscriptionData &&
              subscriptionData.status &&
              ['active', 'trialing'].includes(subscriptionData.status)) {
            return true;
          }
        }

        // If no active subscription found in subscriptions table, check subscription_mappings
        // Get user's email from session
        const userEmail = session.user.email;

        if (userEmail) {
          const { data: mappingData, error: mappingError } = await supabase
            .from('subscription_mappings')
            .select('*')
            .eq('email', userEmail)
            .maybeSingle();

          if (mappingError) {
            console.error('Error checking subscription_mappings:', mappingError);
          } else if (mappingData) {
            // If there's any entry for this email in subscription_mappings, consider them paid
            return true;
          }
        }

        // If we get here, the user does NOT have an active subscription in either table
        return false;
      } catch (dbError) {
        console.error('Database error when checking subscription:', dbError);
        // If there's a database error, assume no subscription to be safe
        return false;
      }
    } catch (error) {
      console.error('Error in checkUserSubscription:', error);
      // In case of error, assume no subscription to be safe
      return false;
    }
  };

  // Function to handle plan selection
  const handleSelectPlan = async (planType: string) => {
    try {

      // Double-check subscription status one more time before redirecting
      try {
        const hasActiveSubscription = await checkUserSubscription();

        if (hasActiveSubscription) {
          setSubscriptionCookie(true);
          setIsLoading(false);
          onClose();
          return;
        }

        // If we get here, the user does NOT have an active subscription
      } catch (subCheckError) {
        console.error('Error in final subscription check:', subCheckError);
        // Continue with redirect if there's an error checking subscription
      }

      // Check if there's a selected plan type from URL parameters
      const selectedPlanType = getSelectedPlanType();

      // Check for no-trial parameters
      const urlParams = new URLSearchParams(window.location.search);
      const basicNoTrial = urlParams.get('basicnotrial') !== null;
      const proNoTrial = urlParams.get('pronotrial') !== null;
      const skipTrial = basicNoTrial || proNoTrial;

      // Get the appropriate price ID based on the selected plan or default to basic
      const priceId = selectedPlanType ?
        getPriceIdForPlanType(selectedPlanType) :
        "price_1ROYLKDebmd1GpTvct491Kw6"; // Default to basic plan if no selection

      try {
        // Get the session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
          throw new Error("No authentication token found");
        }

        const functionUrl = new URL('/functions/v1/stripe-checkout', import.meta.env.VITE_SUPABASE_URL).toString();

        let response;
        try {
          response = await fetch(functionUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${session.access_token}`
            },
            body: JSON.stringify({
              action: "create-checkout-session",
              priceId,
              returnUrl: window.location.origin + "?subscription_success=true",
              skipTrial: skipTrial
            }),
          });

          if (!response.ok) {
            console.error(`Stripe API error: ${response.status} ${response.statusText}`);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        } catch (fetchError) {
          console.error('Error fetching from Stripe API:', fetchError);
          // If we can't reach the Stripe API, redirect to Stripe directly
          // Instead of using hardcoded payment links, create a direct checkout session
          // This is more reliable for international users on all devices
          try {
            // Get the appropriate price ID based on the selected plan or default to basic
            const selectedPlanType = getSelectedPlanType();
            const priceId = selectedPlanType ?
              getPriceIdForPlanType(selectedPlanType) :
              "price_1ROYLKDebmd1GpTvct491Kw6"; // Default to basic plan

            // Get user email from Supabase
            const { data: { session: authSession } } = await supabase.auth.getSession();
            const userEmail = authSession?.user?.email || '';

            // Create a checkout session directly with Stripe
            const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
            if (!stripe) throw new Error("Stripe failed to load");

            // Redirect to Stripe Checkout directly
            window.location.href = `https://checkout.stripe.com/c/pay/${priceId}?prefilled_email=${encodeURIComponent(userEmail)}&locale=auto`;
          } catch (stripeError) {
            console.error('Stripe direct checkout error:', stripeError);
            // Last resort fallback
            window.location.href = 'https://buy.stripe.com/aEU9DL9Xt0H9fO8cMN';
          }
          return;
        }

        let data;
        try {
          data = await response.json();
        } catch (e) {
          throw new Error(`Invalid JSON response`);
        }

        const { sessionId, error } = data;
        if (error) throw new Error(error);
        if (!sessionId) throw new Error("No session ID returned from server");

        const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
        if (!stripe) throw new Error("Stripe failed to load");

        const { error: redirectError } = await stripe.redirectToCheckout({ sessionId });
        if (redirectError) throw new Error(`Stripe redirect failed: ${redirectError.message}`);

        // Clear the selected plan type after successful redirect
        clearSelectedPlanType();
      } catch (authError) {
        console.error('Authentication or Stripe API error:', authError);
        setIsLoading(false);
      }
    } catch (error) {
      // Show error and reset loading state
      console.error('Error redirecting to Stripe:', error);
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-[#101010] font-['SF_Pro_Display']" id="onboarding-modal">
      <div className="min-h-screen flex">
        {/* Left Side - Login Form */}
        <div className="flex-1 flex items-center justify-center px-16 lg:px-24 xl:px-32 relative">
          {/* Logo */}
          <div className="absolute top-8 left-8">
            <img
              src="http://thecodingkid.oyosite.com/logo_only.png"
              alt="Osis"
              className="w-8 h-8 rounded-md"
            />
          </div>

          {isLoggedIn ? (
            // Loading State
            <motion.div
              className="max-w-md w-full"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex flex-col items-center gap-6">
                <Loader2 className="w-12 h-12 text-white animate-spin" />
                <div className="text-center">
                  <h2 className="text-2xl font-semibold text-white mb-2">
                    Checking subscription status...
                  </h2>
                  <p className="text-white/60 text-base">
                    Please wait while we verify your account
                  </p>
                </div>
              </div>
            </motion.div>
          ) : (
            // Login Form
            <motion.div
              className="max-w-md w-full"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="mb-8">
                <h1 className="text-3xl font-normal bg-gradient-to-r from-gray-400 via-gray-300 to-white bg-clip-text text-transparent mb-3">
                  Welcome to Osis
                </h1>
                <p className="text-white/60 text-base">
                  Sign into your google account to continue.
                </p>
              </div>

              <div className="space-y-5">
                {/* Terms checkbox */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="terms"
                    checked={termsAgreed}
                    onCheckedChange={(checked) => setTermsAgreed(checked as boolean)}
                    className="mt-1 data-[state=checked]:bg-white data-[state=checked]:border-white"
                    disabled={isSubmitting}
                  />
                  <label
                    htmlFor="terms"
                    className="text-white/60 text-sm leading-relaxed cursor-pointer"
                  >
                    I agree to the{" "}
                    <a href="/terms" className="text-white hover:underline">Terms</a>
                    {" "}and{" "}
                    <a href="/privacy" className="text-white hover:underline">Privacy Policy</a>
                  </label>
                </div>

                {loginError && (
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                    <p className="text-red-400 text-sm">{loginError}</p>
                  </div>
                )}

                {/* Google Auth Button */}
                <button
                  type="button"
                  onClick={handleGoogleSignIn}
                  disabled={!termsAgreed || isSubmitting}
                  className="w-full bg-white hover:bg-gray-50 text-black font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-[inset_0_1px_0_rgba(255,255,255,0.2),inset_0_-1px_0_rgba(0,0,0,0.1),0_2px_4px_rgba(0,0,0,0.1),0_8px_16px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.3),inset_0_-1px_0_rgba(0,0,0,0.15),0_4px_8px_rgba(0,0,0,0.15),0_12px_24px_rgba(0,0,0,0.15)] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.1),0_1px_2px_rgba(0,0,0,0.1)]"
                >
                  {/* Google icon */}
                  <svg viewBox="0 0 24 24" width="18" height="18">
                    <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                      <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
                      <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
                      <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
                      <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
                    </g>
                  </svg>
                  <span className="text-sm">Continue with Google</span>
                </button>
              </div>
            </motion.div>
          )}
        </div>

        {/* Right Side - Image Container */}
        <div className="flex-1 flex items-center justify-center p-8 bg-gradient-to-br from-[#141414] to-[#0a0a0a]">
          <motion.div
            className="relative bg-white rounded-2xl p-8 shadow-[0_20px_60px_rgba(0,0,0,0.4)] border border-gray-200 max-w-xl w-full"
            initial={{ opacity: 0, scale: 0.9, y: 30 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Content Above Image */}
            <motion.div
              className="text-center mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-3">
                T
              </h2>
              <p className="text-gray-600 text-base leading-relaxed">
                Log in to access your CRM dashboard and manage your team.
              </p>
            </motion.div>

            <div className="overflow-hidden rounded-xl shadow-[0_8px_32px_rgba(0,0,0,0.12)]">
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/homeimages//osis.png"
                alt="Osis Trading Platform"
                className="w-full h-[500px] object-cover"
              />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
